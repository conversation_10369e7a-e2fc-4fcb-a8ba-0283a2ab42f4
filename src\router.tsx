import { createBrowserRouter } from "react-router-dom";
import { ResponsiveLayout } from "./layouts/ResponsiveLayout";
import ProTableRefactored from "./components/ProTableRefactored";
import TestRefactored from "./components/TestRefactored";

export const router = createBrowserRouter([
  {
    path: "/",
    Component: ResponsiveLayout,
    children: [
      {
        index: true,
        Component: () => (
            <ProTableRefactored />
        ),
      },
      {
        path: "protable",
        Component: () => (
            <ProTableRefactored />
        ),
      },
      {
        path: "test",
        Component: TestRefactored,
      },
    ],
  },
]);
