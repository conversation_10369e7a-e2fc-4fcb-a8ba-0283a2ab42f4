{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/icons-vue": "^7.0.1", "@ant-design/pro-components": "^2.8.10", "@tailwindcss/vite": "^4.1.11", "@visactor/react-vtable": "^1.19.5", "@visactor/vtable": "^1.19.5", "antd": "^5.26.7", "axios": "^1.11.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1", "tailwindcss": "^4.1.11", "vite-plugin-style-import": "^2.0.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^24.1.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tailwind-scrollbar": "^4.0.2", "typescript": "~5.8.3", "typescript-eslint": "^8.38.0", "unplugin-auto-import": "^19.3.0", "unplugin-react-components": "^0.1.4", "vite": "^7.0.6"}}