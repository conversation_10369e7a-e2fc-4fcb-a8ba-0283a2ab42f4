import React from "react";
import { VTable } from "react-vtable";

interface TableData {
  id: number;
  name: string;
  age: number;
  email: string;
  department: string;
}

const ReactVTableExample: React.FC = () => {
  // 示例数据
  const data: TableData[] = [
    {
      id: 1,
      name: "<PERSON>",
      age: 28,
      email: "<EMAIL>",
      department: "Engineering",
    },
    {
      id: 2,
      name: "<PERSON>",
      age: 32,
      email: "<EMAIL>",
      department: "Marketing",
    },
    {
      id: 3,
      name: "<PERSON>",
      age: 45,
      email: "<EMAIL>",
      department: "Sales",
    },
    {
      id: 4,
      name: "<PERSON>",
      age: 24,
      email: "<EMAIL>",
      department: "HR",
    },
    // 添加更多数据...
  ];

  // 列配置
  const columns = [
    { key: "id", title: "ID", width: 80 },
    { key: "name", title: "Name", width: 150 },
    { key: "age", title: "Age", width: 80 },
    { key: "email", title: "Email", width: 200 },
    { key: "department", title: "Department", width: 150 },
  ];

  return (
    <div style={{ height: "500px", width: "100%" }}>
      <h2>React VTable with TypeScript</h2>
      <VTable
        data={data}
        columns={columns}
        rowHeight={40}
        headerHeight={50}
        onRowClick={(rowData: TableData) => {
          console.log("Row clicked:", rowData);
        }}
      />
    </div>
  );
};

export default ReactVTableExample;
